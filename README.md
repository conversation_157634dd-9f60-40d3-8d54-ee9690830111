# Cloudflare IP Optimizer

A Python script that replicates the IP optimization functionality from Cloudflare Worker scripts. This tool automatically finds the fastest Cloudflare IP addresses for your location and organizes them by country.

## 🚀 Features

- **Multiple IP Sources**: Fetches IPs from various sources including:
  - Cloudflare official IP ranges
  - ASN-based lists (AS13335, AS209242, AS24429, AS35916, AS199524)
  - Community-maintained lists (CM整理列表)
  - Proxy IP lists
  
- **Intelligent Testing**: 
  - Tests latency using Cloudflare's `/cdn-cgi/trace` endpoint
  - Supports multiple ports (443, 2053, 2083, 2087, 2096, 8443)
  - Concurrent testing with configurable limits
  - Automatic retry mechanism for failed tests

- **Country Organization**: 
  - Automatically detects IP geolocation using Cloudflare's colocation data
  - Organizes results by country code
  - Provides both individual country files and combined results

- **Automated Execution**: 
  - GitHub Actions workflow for scheduled optimization
  - Automatic result commits and releases
  - Configurable scheduling and parameters

## 📋 Requirements

- Python 3.8+
- Dependencies listed in `requirements.txt`

## 🛠️ Installation

1. Clone this repository:
```bash
git clone <your-repo-url>
cd <repo-name>
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

## 💻 Usage

### Command Line Usage

Basic usage with default settings:
```bash
python ip_optimizer.py
```

Advanced usage with custom parameters:
```bash
python ip_optimizer.py \
  --sources official cm proxyip \
  --ports 443 2053 8443 \
  --max-ips 256 \
  --concurrent 16 \
  --timeout 3 \
  --output my_results
```

### Parameters

- `--sources`: IP sources to use (default: official, cm, proxyip)
  - `official`: Cloudflare's official IP ranges
  - `as13335`, `as209242`, `as24429`, `as35916`, `as199524`: ASN-based lists
  - `cm`: Community-maintained Cloudflare CIDR list
  - `proxyip`: Reverse proxy IP list

- `--ports`: Ports to test (default: 443, 2053, 2083, 2087, 2096, 8443)
- `--max-ips`: Maximum IPs to test per source (default: 512)
- `--timeout`: Timeout for each test in seconds (default: 5)
- `--concurrent`: Maximum concurrent tests (default: 32)
- `--output`: Output directory for results (default: results)

## 📊 Output Format

The script generates several output files:

### `all_results.txt`
Contains all optimized IPs sorted by latency:
```
**********:443#US official 45ms
**********:443#US official 52ms
**********:2053#SG official 78ms
```

### Country-specific files (`{country}_ips.txt`)
IPs organized by country code:
```
# Cloudflare IPs for US
# Generated at: 2024-01-15 12:00:00 UTC
# Total IPs: 25

**********:443#US official 45ms
**********:443#US official 52ms
```

### `summary.json`
Statistical summary in JSON format:
```json
{
  "generated_at": "2024-01-15 12:00:00 UTC",
  "total_ips": 150,
  "countries": {
    "US": {
      "count": 25,
      "best_latency": 45.2,
      "avg_latency": 67.8
    }
  }
}
```

## 🤖 Automated Execution

### GitHub Actions Setup

The repository includes a GitHub Actions workflow that automatically:

1. **Runs every 6 hours** to find the latest optimal IPs
2. **Commits results** back to the repository
3. **Creates weekly releases** with optimized IP lists
4. **Supports manual triggering** with custom parameters

### Workflow Configuration

The workflow is configured in `.github/workflows/ip-optimization.yml` and supports:

- **Scheduled execution**: Every 6 hours by default
- **Manual triggering**: Via GitHub Actions UI with custom parameters
- **Automatic commits**: Results are committed back to the repo
- **Weekly releases**: Tagged releases for easy access to historical data

### Manual Trigger

You can manually trigger the workflow from the GitHub Actions tab with custom parameters:

1. Go to Actions tab in your GitHub repository
2. Select "Cloudflare IP Optimization" workflow
3. Click "Run workflow"
4. Customize parameters as needed:
   - IP sources (comma-separated)
   - Ports to test (comma-separated)
   - Maximum IPs per source
   - Concurrent test limit

## 🔧 How It Works

### IP Extraction Algorithm

The script replicates the Cloudflare Worker's IP optimization logic:

1. **IP Source Fetching**:
   - Downloads IP lists from various sources
   - For CIDR ranges: Generates random IPs from each subnet
   - For proxy lists: Parses IP:PORT#COMMENT format

2. **IP Testing Process**:
   - Converts IPs to nip.io format (hex encoding)
   - Tests connectivity to `https://{hex-ip}.nip.io:{port}/cdn-cgi/trace`
   - Measures latency and extracts location data
   - Retries failed tests up to 3 times

3. **Result Organization**:
   - Sorts IPs by latency (fastest first)
   - Groups by country using Cloudflare's colocation data
   - Identifies IP type (official vs proxy)

### Key Features Replicated

- ✅ Multiple IP source support
- ✅ CIDR-based random IP generation
- ✅ Latency testing with Cloudflare's trace endpoint
- ✅ Country detection via colocation data
- ✅ Concurrent testing with rate limiting
- ✅ Result organization by country
- ✅ Automatic retry mechanism
- ✅ IP type detection (official vs proxy)

## 📈 Performance Considerations

- **Concurrent Testing**: Default 32 concurrent connections for optimal speed
- **Rate Limiting**: Built-in semaphore to prevent overwhelming servers
- **Retry Logic**: Automatic retries for failed connections
- **Memory Efficient**: Processes IPs in batches to manage memory usage
- **Timeout Handling**: Configurable timeouts to balance speed vs accuracy

## 🌍 Supported Regions

The script automatically detects and organizes IPs by Cloudflare's global regions including:

- **Americas**: US, CA, BR, MX, etc.
- **Europe**: GB, DE, FR, NL, etc.
- **Asia-Pacific**: SG, JP, HK, AU, etc.
- **Middle East & Africa**: AE, ZA, etc.

## 🔒 Security & Privacy

- **No Data Collection**: Script only tests public Cloudflare endpoints
- **Open Source**: All code is transparent and auditable
- **No Authentication**: Uses only public APIs and endpoints
- **Local Processing**: All optimization happens locally or in your GitHub Actions

## 🤝 Contributing

Contributions are welcome! Please feel free to:

1. Report bugs or issues
2. Suggest new features or improvements
3. Submit pull requests
4. Share optimization results

## 📄 License

This project is open source and available under the MIT License.

## ⚠️ Disclaimer

This tool is for educational and optimization purposes only. Please respect rate limits and terms of service of all external services used. The authors are not responsible for any misuse of this tool.
