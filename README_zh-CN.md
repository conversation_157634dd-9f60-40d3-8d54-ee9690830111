# Cloudflare IP 优化器

一个 Python 脚本，复制了 Cloudflare Worker 脚本中的 IP 优化功能。此工具会自动为您的位置找到最快的 Cloudflare IP 地址，并按国家/地区进行组织。

## 🚀 功能

- **多 IP 源**: 从各种来源获取 IP，包括：
  - Cloudflare 官方 IP 范围
  - 基于 ASN 的列表 (AS13335, AS209242, AS24429, AS35916, AS199524)
  - 社区维护列表 (CM整理列表)
  - 代理 IP 列表

- **智能测试**:
  - 使用 Cloudflare 的 `/cdn-cgi/trace` 端点测试延迟
  - 支持多个端口 (443, 2053, 2083, 2087, 2096, 8443)
  - 可配置并发限制的并发测试
  - 失败测试的自动重试机制

- **国家/地区组织**:
  - 使用 Cloudflare 的托管数据自动检测 IP 地理位置
  - 按国家/地区代码组织结果
  - 提供单独的国家/地区文件和合并结果

- **自动化执行**:
  - 用于计划优化的 GitHub Actions工作流
  - 自动结果提交和发布
  - 可配置的调度和参数

## 📋 要求

- Python 3.8+
- `requirements.txt` 中列出的依赖项

## 🛠️安装

1. 克隆此仓库：
```bash
git clone <your-repo-url>
cd <repo-name>
```

2. 安装依赖项：
```bash
pip install -r requirements.txt
```

## 💻 用法

### 命令行用法

使用默认设置的基本用法：
```bash
python ip_optimizer.py
```

使用自定义参数的高级用法：
```bash
python ip_optimizer.py \
  --sources official cm proxyip \
  --ports 443 2053 8443 \
  --max-ips 256 \
  --concurrent 16 \
  --timeout 3 \
  --output my_results
```

### 参数

- `--sources`: 要使用的 IP 源 (默认: official, cm, proxyip)
  - `official`: Cloudflare 的官方 IP范围
  - `as13335`, `as209242`, `as24429`, `as35916`, `as199524`: 基于ASN 的列表
  - `cm`: 社区维护的 Cloudflare CIDR 列表
  - `proxyip`: 反向代理 IP 列表

- `--ports`: 要测试的端口 (默认: 443, 2053, 2083, 2087, 2096, 8443)
- `--max-ips`: 每个源要测试的最大 IP 数 (默认: 512)
- `--timeout`: 每个测试的超时时间（秒） (默认: 5)
- `--concurrent`: 最大并发测试数 (默认: 32)
- `--output`: 结果输出目录 (默认: results)

## 📊 输出格式

脚本生成几个输出文件：

### `all_results.txt`
包含所有按延迟排序的优化 IP：
```
**********:443#US official 45ms
**********:443#US official 52ms
**********:2053#SG official 78ms
```

### 国家/地区特定文件 (`{country}_ips.txt`)
按国家/地区代码组织的 IP：
```
# Cloudflare IPs for US
# Generated at: 2024-01-15 12:00:00 UTC
# Total IPs: 25

**********:443#US official 45ms
**********:443#US official 52ms
```

### `summary.json`
JSON 格式的统计摘要：
```json
{
  "generated_at": "2024-01-15 12:00:00 UTC",
  "total_ips": 150,
  "countries": {
    "US": {
      "count": 25,
      "best_latency": 45.2,
      "avg_latency": 67.8
    }
  }
}
```

## 🤖 自动化执行

### GitHub Actions 设置

此仓库包含一个 GitHub Actions 工作流，它会自动：

1. **每 6 小时运行一次**以查找最新的最佳 IP
2. **将结果提交**回仓库
3. **每周创建发布**，其中包含优化的 IP 列表
4. **支持手动触发**，带自定义参数

### 工作流配置

工作流在 `.github/workflows/ip-optimization.yml` 中配置，并支持：

- **计划执行**: 默认每 6 小时
- **手动触发**: 通过 GitHub Actions UI，带自定义参数
- **自动提交**: 结果提交回仓库
- **每周发布**: 带标签的发布，便于访问历史数据

### 手动触发

您可以从 GitHub Actions 选项卡手动触发工作流，带自定义参数：

1. 转到 GitHub 仓库中的 Actions 选项卡
2. 选择 "Cloudflare IP Optimization" 工作流
3. 点击 "Run workflow"
4. 根据需要自定义参数：
   - IP 源 (逗号分隔)
   - 要测试的端口 (逗号分隔)
   - 每个源的最大 IP 数
   - 并发测试限制

## 🔧 工作原理

### IP 提取算法

脚本复制了 Cloudflare Worker 的 IP 优化逻辑：

1. **IP 源获取**:
   - 从各种来源下载 IP 列表
   - 对于 CIDR 范围：从每个子网生成随机 IP
   - 对于代理列表：解析 IP:PORT#COMMENT 格式

2. **IP 测试过程**:
   - 将 IP 转换为 nip.io 格式 (十六进制编码)
   - 测试与 `https://{hex-ip}.nip.io:{port}/cdn-cgi/trace` 的连接
   - 测量延迟并提取位置数据
   - 失败测试最多重试 3 次

3. **结果组织**:
   - 按延迟排序 IP (最快优先)
   - 使用 Cloudflare 的托管数据按国家/地区分组
   - 识别 IP 类型 (官方 vs 代理)

### 复制的关键功能

- ✅ 支持多个 IP 源
- ✅ 基于 CIDR 的随机 IP 生成
- ✅ 使用 Cloudflare 的跟踪端点进行延迟测试
- ✅ 通过托管数据进行国家/地区检测
- ✅ 带速率限制的并发测试
- ✅ 按国家/地区组织结果
- ✅ 自动重试机制
- ✅ IP 类型检测 (官方 vs 代理)

## 📈 性能考虑

- **并发测试**: 默认 32 个并发连接以获得最佳速度
- **速率限制**: 内置信号量以防止服务器过载
- **重试逻辑**: 失败连接的自动重试
- **内存效率**: 分批处理 IP 以管理内存使用
- **超时处理**: 可配置的超时以平衡速度与准确性

## 🌍 支持区域

脚本会自动检测并按 Cloudflare 的全球区域组织 IP，包括：

- **美洲**: 美国、加拿大、巴西、墨西哥等
- **欧洲**: 英国、德国、法国、荷兰等
- **亚太地区**: 新加坡、日本、香港、澳大利亚等
- **中东和非洲**: 阿联酋、南非等

## 🔒 安全与隐私

- **不收集数据**: 脚本仅测试公共 Cloudflare 端点
- **开源**: 所有代码透明且可审计
- **无需认证**: 仅使用公共 API 和端点
- **本地处理**: 所有优化都在本地或您的 GitHub Actions 中进行

## 🤝 贡献

欢迎贡献！请随时：

1. 报告错误或问题
2. 提出新功能或改进建议
3. 提交拉取请求
4. 分享优化结果

## 📄 许可证

本项目根据 MIT 许可证开源。

## ⚠️ 免责声明

此工具仅用于教育和优化目的。请遵守所有使用的外部服务的速率限制和服务条款。作者不对本工具的任何滥用负责。
