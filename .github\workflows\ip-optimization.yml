name: Cloudflare IP Optimization

on:
  # Run automatically every 6 hours
  schedule:
    - cron: '0 */6 * * *'
  
  # Allow manual triggering
  workflow_dispatch:
    inputs:
      ip_sources:
        description: 'IP sources to use (comma-separated)'
        required: false
        default: 'official,cm,proxyip'
        type: string
      ports:
        description: 'Ports to test (comma-separated)'
        required: false
        default: '443,2053,2083,2087,2096,8443'
        type: string
      max_ips:
        description: 'Maximum IPs to test per source'
        required: false
        default: '512'
        type: string
      concurrent:
        description: 'Maximum concurrent tests'
        required: false
        default: '32'
        type: string

jobs:
  optimize-ips:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run IP optimization
      run: |
        # Set default values if not provided via workflow_dispatch
        IP_SOURCES="${{ github.event.inputs.ip_sources || 'official,cm,proxyip' }}"
        PORTS="${{ github.event.inputs.ports || '443,2053,2083,2087,2096,8443' }}"
        MAX_IPS="${{ github.event.inputs.max_ips || '512' }}"
        CONCURRENT="${{ github.event.inputs.concurrent || '32' }}"
        
        # Convert comma-separated strings to space-separated for arguments
        IP_SOURCES_ARGS=$(echo "$IP_SOURCES" | tr ',' ' ')
        PORTS_ARGS=$(echo "$PORTS" | tr ',' ' ')
        
        # Run the optimization script
        python ip_optimizer.py \
          --sources $IP_SOURCES_ARGS \
          --ports $PORTS_ARGS \
          --max-ips $MAX_IPS \
          --concurrent $CONCURRENT \
          --output results
    
    - name: Generate README for results
      run: |
        cat > results/README.md << 'EOF'
        # Cloudflare IP Optimization Results
        
        This directory contains optimized Cloudflare IP addresses organized by country and performance.
        
        ## Files Description
        
        - `all_results.txt` - All optimized IPs sorted by latency
        - `summary.json` - Summary statistics in JSON format
        - `{country}_ips.txt` - IPs organized by country code (e.g., `us_ips.txt`, `cn_ips.txt`)
        
        ## File Format
        
        Each IP entry follows the format:
        ```
        IP:PORT#COUNTRY_CODE TYPE LATENCYms
        ```
        
        Where:
        - `IP:PORT` - The IP address and port number
        - `COUNTRY_CODE` - Two-letter country code based on Cloudflare's colocation
        - `TYPE` - Either "official" (Cloudflare's official IPs) or "proxy" (reverse proxy IPs)
        - `LATENCY` - Response time in milliseconds
        
        ## Usage
        
        These optimized IPs can be used to improve connection speeds to Cloudflare services.
        Choose IPs from your region or with the lowest latency for best performance.
        
        ## Last Updated
        
        Generated automatically by GitHub Actions every 6 hours.
        Last run: $(date -u '+%Y-%m-%d %H:%M:%S UTC')
        
        ## Statistics
        
        $(if [ -f results/summary.json ]; then
          echo "### Summary"
          echo "\`\`\`json"
          cat results/summary.json
          echo "\`\`\`"
        fi)
        EOF
    
    - name: Check for changes
      id: check_changes
      run: |
        if [ -n "$(git status --porcelain results/)" ]; then
          echo "changes=true" >> $GITHUB_OUTPUT
          echo "Changes detected in results directory"
        else
          echo "changes=false" >> $GITHUB_OUTPUT
          echo "No changes detected"
        fi
    
    - name: Commit and push results
      if: steps.check_changes.outputs.changes == 'true'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        
        # Add all files in results directory
        git add results/
        
        # Create commit message with timestamp and summary
        TIMESTAMP=$(date -u '+%Y-%m-%d %H:%M:%S UTC')
        
        # Get summary info if available
        if [ -f results/summary.json ]; then
          TOTAL_IPS=$(python -c "import json; data=json.load(open('results/summary.json')); print(data['total_ips'])")
          COUNTRIES=$(python -c "import json; data=json.load(open('results/summary.json')); print(len(data['countries']))")
          COMMIT_MSG="🚀 IP optimization results - $TIMESTAMP

          📊 Summary:
          - Total optimized IPs: $TOTAL_IPS
          - Countries covered: $COUNTRIES
          - Sources: ${{ github.event.inputs.ip_sources || 'official,cm,proxyip' }}
          - Ports tested: ${{ github.event.inputs.ports || '443,2053,2083,2087,2096,8443' }}
          
          Generated by automated workflow"
        else
          COMMIT_MSG="🚀 IP optimization results - $TIMESTAMP"
        fi
        
        git commit -m "$COMMIT_MSG"
        git push
    
    - name: Create release (weekly)
      if: steps.check_changes.outputs.changes == 'true' && github.event.schedule == '0 0 * * 0'
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: results-$(date +%Y%m%d)
        release_name: Weekly IP Optimization Results $(date +%Y-%m-%d)
        body: |
          ## Weekly Cloudflare IP Optimization Results
          
          This release contains the latest optimized Cloudflare IP addresses.
          
          ### What's Included
          - Optimized IPs organized by country
          - Performance metrics and latency data
          - Multiple port configurations tested
          
          ### Usage
          Download the `results.zip` asset and extract to get country-specific IP lists.
          
          Generated automatically on $(date -u '+%Y-%m-%d %H:%M:%S UTC')
        draft: false
        prerelease: false
    
    - name: Upload results as artifact
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: optimization-results-${{ github.run_number }}
        path: results/
        retention-days: 30
    
    - name: Cleanup old artifacts
      uses: actions/github-script@v6
      with:
        script: |
          const artifacts = await github.rest.actions.listWorkflowRunArtifacts({
            owner: context.repo.owner,
            repo: context.repo.repo,
            run_id: context.runId,
          });
          
          // Keep only the latest 10 artifacts
          const sortedArtifacts = artifacts.data.artifacts.sort((a, b) => 
            new Date(b.created_at) - new Date(a.created_at)
          );
          
          for (let i = 10; i < sortedArtifacts.length; i++) {
            await github.rest.actions.deleteArtifact({
              owner: context.repo.owner,
              repo: context.repo.repo,
              artifact_id: sortedArtifacts[i].id,
            });
          }
