#!/usr/bin/env python3
"""
Cloudflare IP Optimizer
Replicates the IP optimization functionality from the Cloudflare Worker script.
Fetches IPs from various sources, tests latency, and organizes results by country.
"""

import asyncio
import aiohttp
import ipaddress
import json
import random
import time
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import argparse

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class IPResult:
    """Represents an IP test result"""
    ip: str
    port: int
    latency: float
    colo: str
    country_code: str
    ip_type: str  # 'official' or 'proxy'
    
    def __str__(self):
        return f"{self.ip}:{self.port}#{self.country_code} {self.ip_type} {self.latency:.0f}ms"

class CloudflareIPOptimizer:
    """Main class for Cloudflare IP optimization"""
    
    def __init__(self, max_ips: int = 512, timeout: int = 5, max_concurrent: int = 32):
        self.max_ips = max_ips
        self.timeout = timeout
        self.max_concurrent = max_concurrent
        self.cloudflare_locations = {}
        self.nip_domain = "nip.io"  # Default fallback
        
    async def load_cloudflare_locations(self) -> None:
        """Load Cloudflare location data for country mapping"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://speed.cloudflare.com/locations') as response:
                    if response.status == 200:
                        locations = await response.json()
                        self.cloudflare_locations = {loc['iata']: loc['cca2'] for loc in locations}
                        logger.info(f"Loaded {len(self.cloudflare_locations)} Cloudflare locations")
                    else:
                        logger.warning("Failed to load Cloudflare locations, using fallback")
        except Exception as e:
            logger.error(f"Error loading Cloudflare locations: {e}")
    
    async def get_nip_domain(self) -> str:
        """Get the current nip domain from DNS TXT record"""
        try:
            # This would require DNS resolution, for now use fallback
            return "nip.io"
        except Exception as e:
            logger.error(f"Error getting nip domain: {e}")
            return "nip.io"
    
    def generate_ips_from_cidr(self, cidr: str, count: int = 1) -> List[str]:
        """Generate random IPs from a CIDR block"""
        try:
            network = ipaddress.IPv4Network(cidr, strict=False)
            # Exclude network and broadcast addresses
            hosts = list(network.hosts())
            if len(hosts) == 0:
                return []
            
            # Limit count to available hosts
            actual_count = min(count, len(hosts))
            return [str(ip) for ip in random.sample(hosts, actual_count)]
        except Exception as e:
            logger.error(f"Error generating IPs from CIDR {cidr}: {e}")
            return []
    
    async def fetch_ip_source(self, source: str, target_port: str = "443") -> List[str]:
        """Fetch IPs from various sources"""
        urls = {
            'official': 'https://www.cloudflare.com/ips-v4/',
            'as13335': 'https://raw.githubusercontent.com/ipverse/asn-ip/master/as/13335/ipv4-aggregated.txt',
            'as209242': 'https://raw.githubusercontent.com/ipverse/asn-ip/master/as/209242/ipv4-aggregated.txt',
            'as24429': 'https://raw.githubusercontent.com/ipverse/asn-ip/master/as/24429/ipv4-aggregated.txt',
            'as35916': 'https://raw.githubusercontent.com/ipverse/asn-ip/master/as/35916/ipv4-aggregated.txt',
            'as199524': 'https://raw.githubusercontent.com/ipverse/asn-ip/master/as/199524/ipv4-aggregated.txt',
            'cm': 'https://raw.githubusercontent.com/cmliu/cmliu/main/CF-CIDR.txt',
            'proxyip': 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/baipiao.txt'
        }
        
        if source not in urls:
            logger.error(f"Unknown IP source: {source}")
            return []
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(urls[source]) as response:
                    if response.status == 200:
                        text = await response.text()
                        return await self._process_ip_source(text, source, target_port)
                    else:
                        logger.error(f"Failed to fetch {source}: HTTP {response.status}")
                        return []
        except Exception as e:
            logger.error(f"Error fetching {source}: {e}")
            return []
    
    async def _process_ip_source(self, text: str, source: str, target_port: str) -> List[str]:
        """Process IP source text based on source type"""
        lines = [line.strip() for line in text.split('\n') if line.strip() and not line.startswith('#')]
        
        if source == 'proxyip':
            # Handle proxy IP list format (IP:PORT#COMMENT)
            valid_ips = []
            for line in lines:
                parsed_ip = self._parse_proxy_ip_line(line, target_port)
                if parsed_ip:
                    valid_ips.append(parsed_ip)
            
            # Limit to max_ips and shuffle if needed
            if len(valid_ips) > self.max_ips:
                valid_ips = random.sample(valid_ips, self.max_ips)
            
            return valid_ips
        else:
            # Handle CIDR format
            ips = set()
            round_num = 1
            
            while len(ips) < self.max_ips and round_num <= 100:
                for cidr in lines:
                    if len(ips) >= self.max_ips:
                        break
                    
                    cidr_ips = self.generate_ips_from_cidr(cidr.strip(), round_num)
                    ips.update(cidr_ips)
                
                round_num += 1
            
            return list(ips)[:self.max_ips]
    
    def _parse_proxy_ip_line(self, line: str, target_port: str) -> Optional[str]:
        """Parse proxy IP line format"""
        try:
            # Handle comment part
            main_part = line
            comment = ""
            if '#' in line:
                main_part, comment = line.split('#', 1)
                main_part = main_part.strip()
                comment = comment.strip()
            
            # Handle port part
            if ':' in main_part:
                ip, port = main_part.split(':', 1)
                ip = ip.strip()
                port = port.strip()
            else:
                ip = main_part.strip()
                port = "443"
            
            # Validate IP format
            try:
                ipaddress.IPv4Address(ip)
            except ipaddress.AddressValueError:
                return None
            
            # Check port match
            if port != target_port:
                return None
            
            # Return formatted result
            result = f"{ip}:{port}"
            if comment:
                result += f"#{comment}"
            
            return result
        except Exception:
            return None
    
    def _ip_to_nip_format(self, ip: str) -> str:
        """Convert IP to nip.io format (hex encoding)"""
        try:
            parts = ip.split('.')
            hex_parts = [f"{int(part):02x}" for part in parts]
            return ''.join(hex_parts)
        except Exception:
            return ip
    
    async def test_single_ip(self, ip: str, port: int) -> Optional[IPResult]:
        """Test a single IP for latency and get location info"""
        max_attempts = 3
        
        for attempt in range(max_attempts):
            try:
                # Convert IP to nip format
                nip_hex = self._ip_to_nip_format(ip)
                test_url = f"https://{nip_hex}.{self.nip_domain}:{port}/cdn-cgi/trace"
                
                start_time = time.time()
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(test_url, timeout=aiohttp.ClientTimeout(total=self.timeout)) as response:
                        if response.status == 200:
                            latency = (time.time() - start_time) * 1000  # Convert to ms
                            trace_text = await response.text()
                            
                            # Parse trace response
                            trace_data = self._parse_trace_response(trace_text)
                            if trace_data and 'colo' in trace_data:
                                colo = trace_data['colo']
                                country_code = self.cloudflare_locations.get(colo, colo)
                                
                                # Determine IP type
                                response_ip = trace_data.get('ip', '')
                                ip_type = 'proxy' if ':' in response_ip or response_ip == ip else 'official'
                                
                                return IPResult(
                                    ip=ip,
                                    port=port,
                                    latency=latency,
                                    colo=colo,
                                    country_code=country_code,
                                    ip_type=ip_type
                                )
                
            except Exception as e:
                if attempt < max_attempts - 1:
                    await asyncio.sleep(0.2)  # Brief delay before retry
                else:
                    logger.debug(f"IP {ip}:{port} failed after {max_attempts} attempts: {e}")
        
        return None
    
    def _parse_trace_response(self, trace_text: str) -> Dict[str, str]:
        """Parse Cloudflare trace response"""
        data = {}
        for line in trace_text.split('\n'):
            line = line.strip()
            if line and '=' in line:
                key, value = line.split('=', 1)
                data[key] = value
        return data

    async def test_ips_with_concurrency(self, ips: List[str], port: int) -> List[IPResult]:
        """Test multiple IPs with controlled concurrency"""
        semaphore = asyncio.Semaphore(self.max_concurrent)

        async def test_with_semaphore(ip: str) -> Optional[IPResult]:
            async with semaphore:
                return await self.test_single_ip(ip, port)

        logger.info(f"Testing {len(ips)} IPs with concurrency {self.max_concurrent}")

        # Create tasks for all IPs
        tasks = [test_with_semaphore(ip.split(':')[0] if ':' in ip else ip) for ip in ips]

        # Execute with progress tracking
        results = []
        completed = 0

        for coro in asyncio.as_completed(tasks):
            result = await coro
            completed += 1

            if result:
                results.append(result)

            # Log progress every 50 completed tests
            if completed % 50 == 0 or completed == len(tasks):
                logger.info(f"Progress: {completed}/{len(tasks)} tested, {len(results)} valid")

        # Sort by latency
        results.sort(key=lambda x: x.latency)
        logger.info(f"Testing completed: {len(results)} valid IPs found")

        return results

    def organize_results_by_country(self, results: List[IPResult]) -> Dict[str, List[IPResult]]:
        """Organize results by country code"""
        country_results = {}

        for result in results:
            country = result.country_code
            if country not in country_results:
                country_results[country] = []
            country_results[country].append(result)

        # Sort each country's results by latency
        for country in country_results:
            country_results[country].sort(key=lambda x: x.latency)

        return country_results

    def save_results(self, results: List[IPResult], output_dir: str = "results") -> None:
        """Save results to files organized by country"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # Organize by country
        country_results = self.organize_results_by_country(results)

        # Save overall results
        with open(output_path / "all_results.txt", "w", encoding="utf-8") as f:
            f.write(f"# Cloudflare IP Optimization Results\n")
            f.write(f"# Generated at: {time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime())}\n")
            f.write(f"# Total IPs tested: {len(results)}\n")
            f.write(f"# Countries found: {len(country_results)}\n\n")

            for result in results:
                f.write(f"{result}\n")

        # Save by country
        for country, country_ips in country_results.items():
            filename = f"{country.lower()}_ips.txt"
            with open(output_path / filename, "w", encoding="utf-8") as f:
                f.write(f"# Cloudflare IPs for {country}\n")
                f.write(f"# Generated at: {time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime())}\n")
                f.write(f"# Total IPs: {len(country_ips)}\n\n")

                for result in country_ips:
                    f.write(f"{result}\n")

        # Save summary JSON
        summary = {
            "generated_at": time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime()),
            "total_ips": len(results),
            "countries": {
                country: {
                    "count": len(ips),
                    "best_latency": min(ip.latency for ip in ips),
                    "avg_latency": sum(ip.latency for ip in ips) / len(ips)
                }
                for country, ips in country_results.items()
            }
        }

        with open(output_path / "summary.json", "w", encoding="utf-8") as f:
            json.dump(summary, f, indent=2)

        logger.info(f"Results saved to {output_path}")
        logger.info(f"Countries found: {', '.join(sorted(country_results.keys()))}")

    async def optimize_ips(self, ip_sources: List[str], ports: List[int], output_dir: str = "results") -> None:
        """Main optimization function"""
        logger.info("Starting Cloudflare IP optimization")

        # Load Cloudflare locations
        await self.load_cloudflare_locations()

        # Get nip domain
        self.nip_domain = await self.get_nip_domain()

        all_results = []

        for port in ports:
            logger.info(f"Testing port {port}")

            for source in ip_sources:
                logger.info(f"Fetching IPs from source: {source}")
                ips = await self.fetch_ip_source(source, str(port))

                if not ips:
                    logger.warning(f"No IPs found for source {source}")
                    continue

                logger.info(f"Found {len(ips)} IPs from {source}")

                # Test IPs
                results = await self.test_ips_with_concurrency(ips, port)
                all_results.extend(results)

                logger.info(f"Source {source} completed: {len(results)} valid IPs")

        if all_results:
            # Sort all results by latency
            all_results.sort(key=lambda x: x.latency)

            # Save results
            self.save_results(all_results, output_dir)

            # Log summary
            country_count = len(set(result.country_code for result in all_results))
            best_latency = all_results[0].latency if all_results else 0

            logger.info(f"Optimization completed!")
            logger.info(f"Total valid IPs: {len(all_results)}")
            logger.info(f"Countries found: {country_count}")
            logger.info(f"Best latency: {best_latency:.0f}ms")
        else:
            logger.error("No valid IPs found!")

async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Cloudflare IP Optimizer")
    parser.add_argument("--sources", nargs="+",
                       default=["official", "cm", "proxyip"],
                       choices=["official", "as13335", "as209242", "as24429", "as35916", "as199524", "cm", "proxyip"],
                       help="IP sources to use")
    parser.add_argument("--ports", nargs="+", type=int,
                       default=[443, 2053, 2083, 2087, 2096, 8443],
                       help="Ports to test")
    parser.add_argument("--max-ips", type=int, default=512,
                       help="Maximum IPs to test per source")
    parser.add_argument("--timeout", type=int, default=5,
                       help="Timeout for each IP test in seconds")
    parser.add_argument("--concurrent", type=int, default=32,
                       help="Maximum concurrent tests")
    parser.add_argument("--output", default="results",
                       help="Output directory for results")

    args = parser.parse_args()

    optimizer = CloudflareIPOptimizer(
        max_ips=args.max_ips,
        timeout=args.timeout,
        max_concurrent=args.concurrent
    )

    await optimizer.optimize_ips(args.sources, args.ports, args.output)

if __name__ == "__main__":
    asyncio.run(main())
