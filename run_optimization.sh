#!/bin/bash

# Cloudflare IP Optimization Runner Script
# This script provides easy presets for common optimization scenarios

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python is available
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed or not in PATH"
        exit 1
    fi
    
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)" 2>/dev/null; then
        print_error "Python 3.8 or higher is required"
        exit 1
    fi
}

# Install dependencies
install_deps() {
    print_info "Installing dependencies..."
    pip3 install -r requirements.txt
    print_success "Dependencies installed successfully"
}

# Preset configurations
run_quick() {
    print_info "Running quick optimization (official + cm sources, port 443 only)..."
    python3 ip_optimizer.py \
        --sources official cm \
        --ports 443 \
        --max-ips 256 \
        --concurrent 16 \
        --timeout 3
}

run_standard() {
    print_info "Running standard optimization (default settings)..."
    python3 ip_optimizer.py
}

run_comprehensive() {
    print_info "Running comprehensive optimization (all sources, all ports)..."
    python3 ip_optimizer.py \
        --sources official as13335 as209242 cm proxyip \
        --ports 443 2053 2083 2087 2096 8443 \
        --max-ips 512 \
        --concurrent 32 \
        --timeout 5
}

run_fast() {
    print_info "Running fast optimization (high concurrency, low timeout)..."
    python3 ip_optimizer.py \
        --sources official cm \
        --ports 443 2053 8443 \
        --max-ips 256 \
        --concurrent 64 \
        --timeout 2
}

run_proxy_only() {
    print_info "Running proxy IP optimization only..."
    python3 ip_optimizer.py \
        --sources proxyip \
        --ports 443 2053 2083 2087 2096 8443 \
        --max-ips 512 \
        --concurrent 32
}

# Show usage
show_usage() {
    echo "Cloudflare IP Optimization Runner"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  quick         Quick optimization (official + cm, port 443 only)"
    echo "  standard      Standard optimization (default settings)"
    echo "  comprehensive Comprehensive optimization (all sources and ports)"
    echo "  fast          Fast optimization (high concurrency, low timeout)"
    echo "  proxy         Proxy IPs only optimization"
    echo "  install       Install dependencies"
    echo "  check         Check system requirements"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 quick                    # Quick optimization"
    echo "  $0 comprehensive            # Full optimization"
    echo "  $0 install                  # Install dependencies"
    echo ""
    echo "For custom parameters, run the Python script directly:"
    echo "  python3 ip_optimizer.py --help"
}

# Main script logic
main() {
    case "${1:-help}" in
        "quick")
            check_python
            run_quick
            ;;
        "standard")
            check_python
            run_standard
            ;;
        "comprehensive")
            check_python
            run_comprehensive
            ;;
        "fast")
            check_python
            run_fast
            ;;
        "proxy")
            check_python
            run_proxy_only
            ;;
        "install")
            check_python
            install_deps
            ;;
        "check")
            check_python
            print_success "System requirements check passed"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
